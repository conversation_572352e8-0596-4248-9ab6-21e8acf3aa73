<template>
    <div class="contentWrapper"> <!-- TODO: das muss anders gelöst werden -->
        <d-listing-detail-toolbar v-if="!isEmbedded"
                                  class="px-2">
            <d-listing-button-back v-if="showBackButton"
                                   :route="selectedPageAndContext && AUTH_SERVICE.isLoggedIn.value
                                            ? listingContextRouteForFlowConfigPage(listingId, selectedPageAndContext[0], selectedPageAndContext[1], true)
                                            : {
                                                name: 'viewListing',
                                                params: {
                                                     id: listingId
                                                }
                                              }
                                   "
                                   :text="t('components.listing.buildingModel.backToListingButton')"
                                   on-primary/>

            <v-slide-x-transition>
                <d-documentation-button v-if="!smAndDown"
                                        :class="{'ms-2': smAndDown, 'ms-3': !smAndDown}"
                                        link-target="CAD"/>
            </v-slide-x-transition>

            <v-spacer/>

            <div :class="{'me-0': smAndDown, 'me-2': !smAndDown}">
                <d-listing-building-cad-visibility-dialog :hide-button-text="smAndDown"
                                                          :renderer="renderer"/>
            </div>

            <v-slide-x-reverse-transition>
                <d-listing-building-cad-history-buttons v-if="renderer.canEdit"
                                                        :class="{'me-2': smAndDown, 'me-3': !smAndDown}"
                                                        :renderer="renderer"/>
            </v-slide-x-reverse-transition>

            <div :class="{'me-2': smAndDown, 'me-3': !smAndDown}">
                <d-btn-toggle v-model="isEvebiModeEnabled"
                              :items="viewTypeItems"
                              :label="smAndDown ? undefined : t('listing.building.cad.displayMode')"
                              :size="smAndDown ? 'small' : undefined"
                              mandatory
                              on-primary/>
            </div>

            <div :class="{'me-0': smAndDown, 'me-2': !smAndDown || (context === 'VIEW' && AUTH_SERVICE.isLoggedIn.value)}">
                <d-btn-toggle v-model="is3DModeEnabled"
                              :disabled="renderer.isLoading.value"
                              :items="rendererModeItems"
                              :label="smAndDown ? undefined : t('listing.building.cad.renderType')"
                              :size="smAndDown ? 'small' : undefined"
                              mandatory
                              on-primary/>
            </div>

            <v-slide-x-reverse-transition>
                <div v-if="!smAndDown && AUTH_SERVICE.isLoggedIn.value"
                     :class="{'me-0': context !== 'VIEW' || !AUTH_SERVICE.isLoggedIn.value, 'me-2': context === 'VIEW' && AUTH_SERVICE.isLoggedIn.value}">
                    <d-listing-building-renderer-wrapper :key="rendererReloadKey"
                                                         :renderer="renderer">

                        <d-listing-building-export-dialog :address="address"
                                                          :building="mutableBuilding"
                                                          :listing-id="listingId"
                                                          variant="BUTTON"/>
                    </d-listing-building-renderer-wrapper>
                </div>
            </v-slide-x-reverse-transition>

            <d-btn v-if="context === 'VIEW' && AUTH_SERVICE.isLoggedIn.value"
                   :disabled="!mayEdit"
                   :icon="smAndDown ? mdiPencil : undefined"
                   :prepend-icon="smAndDown ? undefined : mdiPencil"
                   :size="smAndDown ? 'default' : 'large'"
                   :text="smAndDown ? undefined : t('listing.view.editButton')"
                   :to="{
                          name: 'editListingBuilding',
                          params: {
                            id: listingId,
                            customUIElementId: forcedCustomUIElementId,
                          }
                       }"
                   type="secondary"/>

            <v-dialog :max-width="300"
                      :offset="27"
                      location="bottom center"
                      location-strategy="connected"
                      origin="auto">
                <template #activator="{props: dialogProps}">
                    <v-slide-x-reverse-transition>
                        <d-btn v-if="smAndDown"
                               :icon="mdiDotsVertical"
                               type="default"
                               v-bind="dialogProps"
                               variant="text"/>
                    </v-slide-x-reverse-transition>
                </template>

                <d-card is-in-dialog>
                    <d-list keep-y-padding>
                        <d-documentation-button link-target="CAD"
                                                variant="LIST_ITEM"/>

                        <d-listing-building-renderer-wrapper :key="rendererReloadKey"
                                                             :renderer="renderer">
                            <d-listing-building-export-dialog v-if="AUTH_SERVICE.isLoggedIn.value"
                                                              :address="address"
                                                              :building="mutableBuilding"
                                                              :listing-id="listingId"
                                                              variant="LIST_ITEM"/>
                        </d-listing-building-renderer-wrapper>
                    </d-list>
                </d-card>
            </v-dialog>
        </d-listing-detail-toolbar>

        <div :class="{isEmbedded}"
             class="modelWrapper">
            <v-layout v-if="isEmbedded && (!hideControls || showFullScreenButton)"
                      :class="{
                            hideControls,
                            'pa-2 rounded-xl elevation-2': !hideControls
                        }"
                      class="controlsOverlay position-absolute align-center ma-4 overflow-visible">
                <template v-if="!hideControls">
                    <d-listing-building-cad-visibility-dialog :renderer="renderer"
                                                              hide-button-text/>

                    <div class="ms-1 me-2">
                        <d-btn-toggle v-model="is3DModeEnabled"
                                      :disabled="renderer.isLoading.value"
                                      :items="rendererModeItems"
                                      mandatory/>
                    </div>
                </template>
                <!-- TODO: besser machen mit context === EDIT, gefährlich, wenn neuer context typ hinzukommt -->
                <d-btn v-if="showFullScreenButton"
                       :class="{'mx-1': !hideControls}"
                       :size="isEmbedded && !hideControls ? 'default' : (context === 'EDIT' ? 'large' : 'default')"
                       :text="t(`listing.building.cad.doorbitStudioButton.${WHITE_LABEL_CONTEXT.id}`)"
                       :to="{
                          name: mayEdit && AUTH_SERVICE.isLoggedIn.value ? 'editListingBuilding' : 'viewListingBuilding',
                          params: {
                            id: listingId,
                            customUIElementId: ((mayEdit && AUTH_SERVICE.isLoggedIn.value && rawContext === 'EDIT') || (!(mayEdit && AUTH_SERVICE.isLoggedIn.value) && rawContext === 'VIEW')) ? customUiElementId : rawCustomUIElement?.relatedCustomUIElementId ?? customUiElementId
                          }
                       }"
                       :variant="hideControls ? 'elevated' : undefined"
                       type="tertiary"/>
            </v-layout>
            <!--            <div v-if="cursorFloorPosition"-->
            <!--                 class="cursorInfo text-caption">-->
            <!--                ({{ n(cursorFloorPosition.x, 'decimal') }}, {{ n(cursorFloorPosition.y, 'decimal') }})-->
            <!--            </div>-->
            <d-listing-building-renderer-wrapper :key="rendererReloadKey"
                                                 :renderer="renderer">
                <d-l-b-r :needs-image-download="false"
                         :show-signature="showSignature"
                         :signature-position="isEmbedded ? 'START' : undefined"
                         renderer-id="cad"/>
            </d-listing-building-renderer-wrapper>
        </div>

        <d-listing-building-renderer-wrapper :key="rendererReloadKey"
                                             :renderer="renderer">
            <d-listing-building-toolbar v-if="!hideControls"
                                        :is-embedded="isEmbedded"/>

            <v-slide-x-reverse-transition v-if="!isEmbedded">
                <!-- hier nicht v-if nutzen, da sonst die selection verschwinden kann, während man saved -->
                <d-listing-building-selection v-show="renderer.showSelectionDetails.value"
                                              :custom-ui-element-id="forcedCustomUIElementId"/>
            </v-slide-x-reverse-transition>
        </d-listing-building-renderer-wrapper>
    </div>
    <!-- TODO: loading indicator? -->
</template>

<script lang="ts"
        setup>
    import {computed, inject, onUnmounted, ref, shallowRef, toRef, watch, watchEffect} from "vue";
    import {useDisplay} from "vuetify";
    import {Optional} from "@/model/Optional";
    import {useI18n} from "vue-i18n";
    import {mdiDotsVertical, mdiPencil, mdiVideo2d, mdiVideo3d} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DBtnToggle from "@/adapter/vuetify/theme/components/button/d-btn-toggle.vue";
    import DListingDetailToolbar from "@/components/listing/fragments/d-listing-detail-toolbar.vue";
    import DListingButtonBack from "@/components/listing/fragments/d-listing-button-back.vue";
    import {useListingAddress} from "@/service/use-listing-address";
    import {LFlowDataInjection, LListingContextInjection, LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import {Building, CustomUiElement, FlowConfigPage} from "@/adapter/graphql/generated/graphql";
    import {createMutableBuildingFrom} from "@/components/listing/building/building";
    import {BuildingRenderer3D} from "@/components/listing/building/renderer/BuildingRenderer3D";
    import DListingBuildingSelection from "@/components/listing/building/d-listing-building-selection.vue";
    import {findCustomUIElementById, listingContextRouteForFlowConfigPage, pageFromCustomUIElementId} from "@/components/flow-config/use-flow-config";
    import {ListingContext} from "@/model/listing/ListingContext";
    import {FCFlowConfigInjection} from "@/components/flow-config/FlowConfigInjectionKey";
    import DLBR from "@/components/listing/building/renderer/d-l-b-r.vue";
    import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
    import {BuildingRenderer2D} from "@/components/listing/building/renderer/BuildingRenderer2D";
    import DListingBuildingToolbar from "@/components/listing/building/d-listing-building-toolbar.vue";
    import DListingBuildingRendererWrapper from "@/components/listing/building/d-listing-building-renderer-wrapper.vue";
    import {emptySet} from "@/utility/set";
    import {INTERNET_SPEED, InternetSpeed} from "@/service/pwa/internet-speed";
    import {useRouter} from "vue-router";
    import {AUTH_SERVICE} from "@/service/auth/AuthService";
    import {WHITE_LABEL_CONTEXT} from "@/service/white-label/white-label-context";
    import {HistoryManager} from "@/utility/history-manager";
    import DListingBuildingExportDialog from "@/components/listing/building/d-listing-building-export-dialog.vue";
    import DListingBuildingCadHistoryButtons from "@/components/listing/building/d-listing-building-cad-history-buttons.vue";
    import {DBtnToggleItem} from "@/adapter/vuetify/theme/components/button/d-btn-toggle";
    import doorbitLogoIcon from "@/assets/icons/doorbit.svg?url";
    import evebiLogoIcon from "@/assets/icons/evebi.png?url";
    import DDocumentationButton from "@/components/layout/d-documentation-button.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DListingBuildingCadVisibilityDialog from "@/components/listing/building/d-listing-building-cad-visibility-dialog.vue";

    const props = defineProps<{
        customUiElementId: string
        building: Building
        isEmbedded: boolean
        hideControls: boolean
        showFullScreenButton: boolean
        showSignature: boolean
        showBackButton: boolean
        initializeWith3d: boolean
    }>()

    const propsBuilding = toRef(() => props.building)
    const mayEdit = computed<boolean>(() => INTERNET_SPEED.value > InternetSpeed.OFFLINE)
    const rawContext = inject(LListingContextInjection)!
    const context = computed<ListingContext>(() => mayEdit.value ? rawContext.value : 'VIEW')
    const forcedCustomUIElementId = computed<string>(() => {
        const rawCntxt = rawContext.value
        const canEdit = mayEdit.value && AUTH_SERVICE.isLoggedIn.value

        return ((canEdit && rawCntxt === "EDIT") || rawCntxt === "VIEW") ? props.customUiElementId : rawCustomUIElement.value?.relatedCustomUIElementId ?? props.customUiElementId
    })

    if (!props.isEmbedded) {
        const router = useRouter()
        watch([rawContext, context], parameters => {
            const [rawContext, context] = parameters

            if (rawContext === 'EDIT' && context === 'VIEW') {
                router.replace({
                    name: 'viewListingBuilding',
                    params: {
                        id: listingId.value,
                        customUIElementId: forcedCustomUIElementId.value,
                    }
                })
            }
        })
    }

    // const cursorFloorPosition = computed<Optional<Vector2>>(() => {
    //     const rend = renderer.value
    //     if (rend === null) {
    //         return null
    //     }
    //     if (rend.trackingRaycaster === null) {
    //         return null
    //     }
    //     const currentFloor = rend.currentFloor.value
    //     if (currentFloor === null) {
    //         return null
    //     }
    //
    //     rend.trackingRaycaster.moveUpdateCounter.value //trigger update
    //
    //     const buildingTransformation = transformationMatrixOfShapeRepresentation(rend.building.value.shapeRepresentation)
    //     const floorTransformation = transformationMatrixOfShapeRepresentation(currentFloor.shapeRepresentation)
    //     const floorWorldTransformation = buildingTransformation.clone().multiply(floorTransformation)
    //     const inverseFloorWorldTransformation = floorWorldTransformation.clone().invert()
    //
    //     const cursorPosition = rend.trackingRaycaster.intersectionWorldPosition.clone().applyMatrix4(inverseFloorWorldTransformation)
    //
    //     return new Vector2(cursorPosition.x, cursorPosition.z)
    // })

    const mutableBuilding = ref<Building>(createMutableBuildingFrom(propsBuilding.value)) //muss ref sein, da wir jede kleine Änderung am building haben wollen

    const rendererModeItems: readonly DBtnToggleItem<boolean>[] = [{
        icon: mdiVideo2d,
        value: false,
    }, {
        icon: mdiVideo3d,
        value: true,
    }]

    const isEvebiModeEnabled = computed<boolean>({
        get: () => renderer.value.isEvebiModeEnabled.value,
        set: value => {
            const rend = renderer.value

            rend.isEvebiModeEnabled.value = value

            if (value) {
                if (rend.mode.value === "POI_ADDING" || rend.mode.value === "OPENING_CREATION" || rend.mode.value === "WALL_CREATION") {
                    rend.mode.value = "DEFAULT"
                }
            }
        }
    })
    const viewTypeItems = computed<readonly DBtnToggleItem<boolean>[]>(() => [{
        text: smAndDown.value ? undefined : "doorbit",
        image: doorbitLogoIcon,
        value: false,
    }, {
        text: smAndDown.value ? undefined : "Evebi",
        image: evebiLogoIcon,
        value: true,
    }])

    watch(propsBuilding, building => {
        mutableBuilding.value = createMutableBuildingFrom(building)
    }, {
        deep: true //das ist wichtig, damit auch die innere Struktur des building beobachtet wird
    })

    const flowConfig = inject(FCFlowConfigInjection)!
    const selectedPageAndContext = computed<Optional<[FlowConfigPage, ListingContext]>>(() => pageFromCustomUIElementId(flowConfig.value, forcedCustomUIElementId.value))
    const rawCustomUIElement = computed<Optional<CustomUiElement>>(() => findCustomUIElementById(flowConfig.value, props.customUiElementId))

    const listingId = inject(LListingIdInjection)!
    const flowData = inject(LFlowDataInjection)!

    const {
        smAndDown,
    } = useDisplay();
    const {t, n} = useI18n()

    const is3DModeEnabled = shallowRef<boolean>(props.initializeWith3d || true)

    function createRenderer(is3DModeEnabled: boolean, canEdit: boolean, previousHistoryManager: Optional<HistoryManager<Building>>): BuildingRenderer {
        if (is3DModeEnabled) {
            return new BuildingRenderer3D(
                listingId.value,
                mutableBuilding,
                canEdit,
                previousHistoryManager,
            )
        }
        return new BuildingRenderer2D(
            listingId.value,
            mutableBuilding,
            canEdit,
            previousHistoryManager,
        )
    }

    function calculateNewRenderer(oldRenderer: Optional<BuildingRenderer>, is3DModeEnabled: boolean, context: ListingContext): BuildingRenderer {
        const newRenderer = createRenderer(
            is3DModeEnabled,
            context === 'EDIT',
            oldRenderer?.historyManager ?? null,
        )
        const building = newRenderer.building.value

        const oldVisibleFloorLevels = oldRenderer?.visibleFloorLevels.value ?? emptySet()
        if (oldVisibleFloorLevels.size <= 0) {
            newRenderer.visibleFloorLevels.value = building.floors.length > 0 ? new Set([building.floors[0].level]) : emptySet()
        } else if (oldVisibleFloorLevels.size <= 1) {
            newRenderer.visibleFloorLevels.value = oldVisibleFloorLevels
        } else {
            switch (newRenderer.renderType) {
                case "2D":
                    newRenderer.visibleFloorLevels.value = new Set([oldVisibleFloorLevels.values().next().value!])
                    break
                case "3D":
                    newRenderer.visibleFloorLevels.value = oldVisibleFloorLevels
                    break
            }
        }

        newRenderer.visibleFloorLevels.value = new Set([2])

        if (oldRenderer !== null) {
            newRenderer.semiVisibleFloorLevels.value = oldRenderer.semiVisibleFloorLevels.value
            newRenderer.showFurniture.value = oldRenderer.showFurniture.value
            newRenderer.showCompass.value = oldRenderer.showCompass.value
            newRenderer.showFloorGrid.value = oldRenderer.showFloorGrid.value
            newRenderer.showRoomTexts.value = oldRenderer.showRoomTexts.value
            newRenderer.visibleWallTypes.value = oldRenderer.visibleWallTypes.value
            newRenderer.showPointsOfInterest.value = oldRenderer.showPointsOfInterest.value
            newRenderer.showWallWidths.value = oldRenderer.showWallWidths.value
            newRenderer.showWallThicknesses.value = oldRenderer.showWallThicknesses.value
            newRenderer.showDisplayIds.value = oldRenderer.showDisplayIds.value
            newRenderer.showRoofAreas.value = oldRenderer.showRoofAreas.value
            newRenderer.isEvebiModeEnabled.value = oldRenderer.isEvebiModeEnabled.value

            if (newRenderer.wallOpeningCreator !== null && oldRenderer.wallOpeningCreator) {
                newRenderer.wallOpeningCreator.openingType.value = oldRenderer.wallOpeningCreator.openingType.value
            }
        }

        oldRenderer?.destroy()

        return newRenderer
    }

    const renderer = shallowRef<BuildingRenderer>(calculateNewRenderer(null, is3DModeEnabled.value, context.value))

    onUnmounted(() => {
        renderer.value.destroy()
    })

    watch([is3DModeEnabled, context], parameters => {
        const [is3DModeEnabled, context] = parameters

        renderer.value = calculateNewRenderer(renderer.value, is3DModeEnabled, context)
    })

    const rendererReloadKey = shallowRef<number>(0)

    const {
        address
    } = useListingAddress(flowData)

    // watchEffect(() => {
    //     console.log(JSON.stringify(renderer.value.hoverRaycaster.hoveredRaycasterObjects.value.map(o => {
    //         const compo = toRaw(o.component)
    //
    //         const b = renderer.value.building.value
    //         const floor = b && compo.__typename !== "Building" && compo.__typename !== "Floor" ? findRelatedFloorForChildComponent(b, compo as Room | Wall | Furniture | ConstructionPart) : null
    //
    //         return `${o.type} (${compo.id}): Floor ${floor?.level ?? "unknown"}`
    //     })))
    // })

    watchEffect(() => {
        const rend = renderer.value
        rend.selectionRaycaster.isEnabled = !props.isEmbedded
        rend.hoverRaycaster.isEnabled = !props.isEmbedded
    })

    watch(renderer, () => {
        ++rendererReloadKey.value
    })
</script>

<style scoped>
    .contentWrapper {
        position: relative;
        height: 100%;
        width: 100%;
    }

    .modelWrapper {
        width: 100%;
        height: 100%;
        padding-top: calc(var(--v-d-native-app-top-bar-height) + env(safe-area-inset-top));
    }

    .controlsOverlay {
        right: 0;
        bottom: 0;
        z-index: 1004;
    }

    .controlsOverlay:not(.hideControls) {
        background: rgb(var(--v-theme-on-surface-variant));
    }

    /*.cursorInfo {
        position: absolute;
        padding-top: calc(var(--v-d-native-app-top-bar-height) + env(safe-area-inset-top));
        top: 0;
        left: 0;
        z-index: 1;
    }*/

    .modelWrapper.isEmbedded,
    .modelWrapper.isEmbedded .cursorInfo {
        padding-top: 0;
    }
</style>