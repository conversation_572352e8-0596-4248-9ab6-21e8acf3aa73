<template>
    <t-mesh :render-order="9999"
            :transformation="transformation"
            :visible="renderer.mode.value === 'POI_ADDING' && renderer.hoverRaycaster.intersectionTransformationMatrix.value !== null"
            add-to-scene>
        <t-sphere-geometry :radius="sphereRadius"/>

        <t-mesh-basic-material :color="0xFF0000"
                               :depth-test="false"
                               transparent/>

        <t-mesh :render-order="9998">
            <t-plane-geometry :height="planeSize"
                              :width="planeSize"/>

            <t-mesh-basic-material :color="0xFFFFFF"
                                   :depth-test="false"
                                   :opacity="0.5"
                                   transparent/>
        </t-mesh>

        <t-lines v-if="renderer.renderType === '3D'"
                 :material="lineMaterial"
                 :render-order="9999"
                 :transformation="lineTransformation"
                 :vertices="lineVertices"
                 type="LINE"/>
    </t-mesh>
</template>

<script lang="ts"
        setup>
    import {computed, inject, onUnmounted} from "vue";
    import {DBuildingRendererInjection} from "@/components/listing/building/building";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import {LineBasicMaterial, Matrix4, Shape, Vector3} from "three";
    import {tShapeToLinesSegments} from "@/adapter/three/three-utility";
    import TLines from "@/adapter/three/components/object/t-lines.vue";
    import TSphereGeometry from "@/adapter/three/components/geometry/t-sphere-geometry.vue";
    import TPlaneGeometry from "@/adapter/three/components/geometry/t-plane-geometry.vue";

    const lineMaterial = new LineBasicMaterial({
        color: 0xFF0000,
        depthTest: false,
        transparent: true,
    })

    const scale = 1.5
    const sphereRadius = 0.05 * scale
    const planeSize = 0.25 * scale
    const planeHalfSize = planeSize / 2

    onUnmounted(() => {
        lineMaterial.dispose()
    })

    const renderer = inject(DBuildingRendererInjection)!
    const transformation = computed<Matrix4>(() => renderer.hoverRaycaster.intersectionTransformationMatrix.value ?? new Matrix4().identity())

    const shape = new Shape()
    shape.moveTo(-planeHalfSize, -planeHalfSize)
    shape.lineTo(planeHalfSize, -planeHalfSize)
    shape.lineTo(planeHalfSize, planeHalfSize)
    shape.lineTo(-planeHalfSize, planeHalfSize)
    shape.lineTo(-planeHalfSize, -planeHalfSize)
    const lineVertices = tShapeToLinesSegments(shape, 1).map(v => new Vector3(v.x, 0, v.y))

    const lineTransformation = new Matrix4().makeRotationX(Math.PI / 2)
</script>

<style scoped>
</style>